#!/usr/bin/env python3
"""
Android代码保护快速设置脚本
一键配置混淆和加密保护
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """检查环境要求"""
    print("🔍 检查环境要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 需要Python 3.6或更高版本")
        return False
    
    # 检查必要的包
    required_packages = ['cryptography']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"📦 安装缺失的包: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("✅ 依赖包安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖包安装失败，请手动安装:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    
    return True

def find_android_project():
    """查找Android项目"""
    current_dir = Path.cwd()
    
    # 检查当前目录
    if (current_dir / "app" / "build.gradle").exists():
        return current_dir
    
    # 检查子目录
    for item in current_dir.iterdir():
        if item.is_dir() and (item / "app" / "build.gradle").exists():
            return item
    
    return None

def main():
    print("🛡️  Android代码保护工具 - 快速设置")
    print("=" * 50)
    
    # 检查环境
    if not check_requirements():
        sys.exit(1)
    
    # 查找Android项目
    project_root = find_android_project()
    if not project_root:
        print("❌ 未找到Android项目")
        print("请确保在Android项目根目录运行此脚本")
        sys.exit(1)
    
    print(f"📱 找到Android项目: {project_root}")
    
    # 运行主保护工具
    protector_script = Path(__file__).parent / "android_code_protector.py"
    
    if not protector_script.exists():
        print("❌ 未找到android_code_protector.py")
        sys.exit(1)
    
    try:
        # 切换到项目目录
        os.chdir(project_root)
        
        # 运行设置
        result = subprocess.run([
            sys.executable, 
            str(protector_script), 
            'setup',
            '--project-root', 
            str(project_root)
        ], check=True)
        
        print("\n🎉 设置完成!")
        print("\n📋 下一步操作:")
        print("1. 检查 app/proguard-rules.pro 混淆配置")
        print("2. 检查 app/build.gradle 构建配置")
        print("3. 运行以下命令测试:")
        print(f"   cd {project_root}")
        print("   python android_code_protector.py encrypt")
        print("   python android_code_protector.py decrypt")
        print("\n4. 在Android Studio中构建项目测试")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 设置失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n❌ 设置被中断")
        sys.exit(1)

if __name__ == '__main__':
    main()
