#!/usr/bin/env python3
"""
Android Java代码保护工具
支持源码加密、混淆配置和自动化构建集成
"""

import os
import sys
import shutil
import json
import argparse
import hashlib
from pathlib import Path
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import zlib
import time

class AndroidCodeProtector:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root).resolve()
        self.config_file = self.project_root / "code_protection_config.json"
        self.key_file = self.project_root / "protection_key.key"
        self.backup_dir = self.project_root / ".code_backup"
        
        # 默认配置
        self.default_config = {
            "java_dirs": ["app/src/main/java"],
            "protected_packages": [],  # 空表示保护所有包
            "exclude_files": ["BuildConfig.java", "R.java"],
            "encryption_enabled": True,
            "obfuscation_enabled": True,
            "backup_enabled": True,
            "key_derivation_iterations": 100000
        }
        
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        else:
            self.config = self.default_config.copy()
            self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def generate_key(self, password=None):
        """生成或加载加密密钥"""
        if self.key_file.exists():
            # 如果密钥文件已存在，使用load_key方法加载
            return self.load_key(password)

        if password is None:
            # 使用项目路径作为默认密码（固定值，确保一致性）
            password = str(self.project_root).encode()
        elif isinstance(password, str):
            password = password.encode()

        # 使用PBKDF2生成密钥
        salt = os.urandom(16)
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=self.config['key_derivation_iterations'],
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))

        # 保存密钥和盐
        key_data = {
            'key': base64.b64encode(key).decode(),
            'salt': base64.b64encode(salt).decode(),
            'iterations': self.config['key_derivation_iterations']
        }

        with open(self.key_file, 'w') as f:
            json.dump(key_data, f)

        print(f"✓ 密钥生成完成: {self.key_file}")
        return key
    
    def load_key(self, password=None):
        """加载加密密钥"""
        if not self.key_file.exists():
            return self.generate_key(password)

        with open(self.key_file, 'r') as f:
            key_data = json.load(f)

        # 如果密钥文件中直接存储了密钥，直接返回
        if password is None and 'key' in key_data:
            try:
                return base64.b64decode(key_data['key'])
            except:
                pass

        # 使用密码派生密钥
        if password is None:
            # 使用项目路径作为默认密码（固定值）
            password = str(self.project_root).encode()
        elif isinstance(password, str):
            password = password.encode()

        salt = base64.b64decode(key_data['salt'])
        iterations = key_data['iterations']

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=iterations,
        )

        try:
            key = base64.urlsafe_b64encode(kdf.derive(password))
            return key
        except Exception as e:
            print(f"❌ 密钥派生失败: {e}")
            # 尝试直接使用存储的密钥
            try:
                return base64.b64decode(key_data['key'])
            except Exception as e2:
                print(f"❌ 加载存储密钥失败: {e2}")
                raise e2
    
    def should_protect_file(self, file_path):
        """判断文件是否需要保护"""
        file_path = Path(file_path)
        
        # 检查排除文件
        if file_path.name in self.config['exclude_files']:
            return False
        
        # 检查包路径
        if self.config['protected_packages']:
            relative_path = str(file_path.relative_to(self.project_root))
            for package in self.config['protected_packages']:
                if package.replace('.', '/') in relative_path:
                    return True
            return False
        
        return True
    
    def create_backup(self):
        """创建源码备份"""
        if not self.config['backup_enabled']:
            return
        
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        
        self.backup_dir.mkdir(exist_ok=True)
        
        for java_dir in self.config['java_dirs']:
            src_dir = self.project_root / java_dir
            if src_dir.exists():
                dst_dir = self.backup_dir / java_dir
                dst_dir.parent.mkdir(parents=True, exist_ok=True)
                shutil.copytree(src_dir, dst_dir)
        
        print(f"✓ 源码备份完成: {self.backup_dir}")
    
    def restore_backup(self):
        """恢复源码备份"""
        if not self.backup_dir.exists():
            print("❌ 没有找到备份文件")
            return False
        
        for java_dir in self.config['java_dirs']:
            backup_src = self.backup_dir / java_dir
            if backup_src.exists():
                dst_dir = self.project_root / java_dir
                if dst_dir.exists():
                    shutil.rmtree(dst_dir)
                shutil.copytree(backup_src, dst_dir)
        
        print("✓ 源码恢复完成")
        return True
    
    def encrypt_file(self, file_path, cipher_suite):
        """加密单个Java文件"""
        with open(file_path, 'rb') as f:
            content = f.read()

        # 压缩后加密
        compressed = zlib.compress(content, level=9)
        encrypted = cipher_suite.encrypt(compressed)

        # 生成加密文件 - 正确处理Path对象
        if isinstance(file_path, Path):
            encrypted_path = file_path.with_suffix('.jenc')
        else:
            encrypted_path = str(file_path).replace('.java', '.jenc')

        with open(encrypted_path, 'wb') as f:
            f.write(encrypted)

        # 删除原文件
        os.remove(file_path)
        return str(encrypted_path)
    
    def decrypt_file(self, encrypted_path, cipher_suite):
        """解密Java文件"""
        with open(encrypted_path, 'rb') as f:
            encrypted = f.read()

        try:
            # 解密后解压缩
            compressed = cipher_suite.decrypt(encrypted)
            content = zlib.decompress(compressed)

            # 恢复原文件 - 正确处理Path对象
            if isinstance(encrypted_path, Path):
                original_path = encrypted_path.with_suffix('.java')
            else:
                original_path = str(encrypted_path).replace('.jenc', '.java')

            with open(original_path, 'wb') as f:
                f.write(content)

            # 删除加密文件
            os.remove(encrypted_path)
            return str(original_path)
        except Exception as e:
            print(f"❌ 解密失败 {encrypted_path}: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def process_directory(self, operation, password=None):
        """处理目录中的Java文件"""
        # 使用安全的输出方式，避免Unicode编码问题
        try:
            print(f"[INFO] 开始{operation}操作...")

            key = self.load_key(password)
            cipher_suite = Fernet(key)
            print("[INFO] 密钥加载成功")
        except Exception as e:
            print(f"[ERROR] 密钥加载失败: {e}")
            return []

        processed_files = []
        total_files_found = 0

        for java_dir in self.config['java_dirs']:
            dir_path = self.project_root / java_dir
            if not dir_path.exists():
                print(f"[WARN] 目录不存在: {dir_path}")
                continue

            print(f"[INFO] 扫描目录: {dir_path}")

            if operation == 'encrypt':
                pattern = "*.java"
                file_filter = lambda p: p.suffix == '.java'
                print("[INFO] 查找 .java 文件...")
            else:
                pattern = "*.jenc"
                file_filter = lambda p: p.suffix == '.jenc'
                print("[INFO] 查找 .jenc 文件...")

            # 使用递归搜索
            found_files = list(dir_path.rglob(pattern))
            total_files_found += len(found_files)
            print(f"[INFO] 找到 {len(found_files)} 个 {pattern} 文件")

            for file_path in found_files:
                if self.should_protect_file(file_path):
                    try:
                        if operation == 'encrypt':
                            result = self.encrypt_file(file_path, cipher_suite)
                            print(f"[ENCRYPT] {file_path.relative_to(self.project_root)}")
                        else:
                            result = self.decrypt_file(file_path, cipher_suite)
                            if result:
                                print(f"[DECRYPT] {Path(result).relative_to(self.project_root)}")

                        if result:
                            processed_files.append(result)
                    except Exception as e:
                        print(f"[ERROR] 处理失败 {file_path}: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    print(f"[SKIP] 跳过文件: {file_path.name}")

        if total_files_found == 0:
            if operation == 'encrypt':
                print("[WARN] 没有找到 .java 文件，请检查项目结构")
            else:
                print("[WARN] 没有找到 .jenc 文件，可能还没有加密过")

        return processed_files
    
    def generate_proguard_config(self):
        """生成ProGuard混淆配置"""
        proguard_config = """
# Android代码保护 - ProGuard配置 (修复版)
# 解决R8混淆错误和缺失类问题

# 基本混淆设置
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose

# 保持源文件名和行号信息（调试用）
-keepattributes SourceFile,LineNumberTable

# 保持注解
-keepattributes *Annotation*
-keep class * extends java.lang.annotation.Annotation { *; }

# Android组件保护
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.preference.Preference
-keep public class * extends android.view.View

# Fragment
-keep public class * extends android.app.Fragment
-keep public class * extends android.support.v4.app.Fragment
-keep public class * extends androidx.fragment.app.Fragment

# 序列化和Parcelable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# 反射和JNI
-keepclasseswithmembernames class * {
    native <methods>;
}

-keepclassmembers class * {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# 枚举类型
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 解决javax.lang.model相关错误
-dontwarn javax.lang.model.**
-dontwarn javax.annotation.processing.**
-dontwarn javax.tools.**
-keep class javax.** { *; }

# Google相关库
-dontwarn com.google.errorprone.annotations.**
-keep class com.google.errorprone.annotations.** { *; }

# AndroidX相关
-keep class androidx.** { *; }
-dontwarn androidx.**
-keep class androidx.lifecycle.** { *; }
-dontwarn androidx.lifecycle.**

# 日志移除
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# 警告处理
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn sun.misc.Unsafe
-dontwarn java.lang.invoke.**

# 保持泛型信息
-keepattributes Signature
-keepattributes InnerClasses
-keepattributes Exceptions

# 类名混淆
-repackageclasses ''
-allowaccessmodification
"""
        
        proguard_file = self.project_root / "app" / "proguard-rules.pro"
        proguard_file.parent.mkdir(exist_ok=True)
        
        with open(proguard_file, 'w', encoding='utf-8') as f:
            f.write(proguard_config.strip())
        
        print(f"✓ ProGuard配置生成: {proguard_file}")
        return proguard_file

    def update_build_gradle(self):
        """更新build.gradle文件以启用混淆和集成加密"""
        build_gradle = self.project_root / "app" / "build.gradle"

        if not build_gradle.exists():
            print("❌ 未找到app/build.gradle文件")
            return False

        # 读取现有内容
        with open(build_gradle, 'r', encoding='utf-8') as f:
            content = f.read()

        # 检查是否已经配置
        if "// Android代码保护配置" in content:
            print("✓ build.gradle已经配置过代码保护")
            return True

        # 备份原文件
        shutil.copy2(build_gradle, str(build_gradle) + ".backup")

        # 只添加自定义任务，不修改现有的buildTypes配置
        # 这样避免破坏现有的gradle结构

        # 添加自定义任务（移除emoji字符，避免编码问题）
        gradle_tasks = """

// Android代码保护配置 - 自动生成
task decryptJavaFiles(type: Exec) {
    workingDir project.projectDir.parent
    commandLine 'python', 'android_code_protector.py', 'decrypt'
    doFirst {
        println "开始解密Java文件..."
    }
    doLast {
        println "Java文件解密完成"
    }
    ignoreExitValue true
}

task encryptJavaFiles(type: Exec) {
    workingDir project.projectDir.parent
    commandLine 'python', 'android_code_protector.py', 'encrypt'
    doFirst {
        println "开始加密Java文件..."
    }
    doLast {
        println "Java文件加密完成"
    }
    ignoreExitValue true
}

task setupCodeProtection(type: Exec) {
    workingDir project.projectDir.parent
    commandLine 'python', 'android_code_protector.py', 'setup'
    doFirst {
        println "配置代码保护..."
    }
    doLast {
        println "代码保护配置完成"
    }
}

// 集成到构建流程
android.applicationVariants.all { variant ->
    // 构建前解密
    variant.preBuildProvider.get().dependsOn(decryptJavaFiles)

    // 构建后加密
    variant.assembleProvider.get().finalizedBy(encryptJavaFiles)
}"""

        # 简单地在文件末尾添加任务，不修改现有结构
        content += gradle_tasks

        # 写入新内容
        with open(build_gradle, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"✓ build.gradle配置完成 (备份: {build_gradle}.backup)")
        return True

    def setup_protection(self):
        """设置完整的代码保护环境"""
        print("🛡️  开始设置Android代码保护...")

        # 1. 创建备份
        self.create_backup()

        # 2. 生成ProGuard配置
        self.generate_proguard_config()

        # 3. 更新build.gradle
        self.update_build_gradle()

        # 4. 生成密钥
        self.generate_key()

        print("✅ 代码保护设置完成!")
        print("\n📋 使用说明:")
        print("1. 运行 'python android_code_protector.py encrypt' 加密源码")
        print("2. 运行 'python android_code_protector.py decrypt' 解密源码")
        print("3. 在Android Studio中正常构建项目，会自动处理加密/解密")
        print("4. 发布版本会自动启用ProGuard混淆")

        return True

def main():
    parser = argparse.ArgumentParser(
        description='Android Java代码保护工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python android_code_protector.py setup          # 初始化代码保护环境
  python android_code_protector.py encrypt        # 加密Java源码
  python android_code_protector.py decrypt        # 解密Java源码
  python android_code_protector.py backup         # 创建源码备份
  python android_code_protector.py restore        # 恢复源码备份
        """
    )

    parser.add_argument(
        'operation',
        choices=['setup', 'encrypt', 'decrypt', 'backup', 'restore'],
        help='操作类型'
    )

    parser.add_argument(
        '--project-root',
        default='.',
        help='Android项目根目录 (默认: 当前目录)'
    )

    parser.add_argument(
        '--password',
        help='加密密码 (可选，默认自动生成)'
    )

    parser.add_argument(
        '--config',
        help='自定义配置文件路径'
    )

    args = parser.parse_args()

    try:
        protector = AndroidCodeProtector(args.project_root)

        if args.config:
            protector.config_file = Path(args.config)
            protector.load_config()

        if args.operation == 'setup':
            protector.setup_protection()

        elif args.operation == 'encrypt':
            files = protector.process_directory('encrypt', args.password)
            print(f"[SUCCESS] 加密完成，处理了 {len(files)} 个文件")

        elif args.operation == 'decrypt':
            files = protector.process_directory('decrypt', args.password)
            print(f"[SUCCESS] 解密完成，处理了 {len(files)} 个文件")

        elif args.operation == 'backup':
            protector.create_backup()

        elif args.operation == 'restore':
            protector.restore_backup()

    except KeyboardInterrupt:
        print("\n[ERROR] 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] 错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
