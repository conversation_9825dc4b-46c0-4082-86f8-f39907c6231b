# Android代码保护 - ProGuard混淆配置模板
# 可以根据项目需要进行调整

# ================================
# 基本混淆设置
# ================================
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*

# 保持源文件名和行号信息（调试用，发布时可以注释掉）
# -keepattributes SourceFile,LineNumberTable

# 保持注解
-keepattributes *Annotation*
-keep class * extends java.lang.annotation.Annotation { *; }

# ================================
# Android组件保护
# ================================
# 保持Activity
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.preference.Preference
-keep public class * extends android.view.View

# 保持Fragment
-keep public class * extends android.app.Fragment
-keep public class * extends android.support.v4.app.Fragment
-keep public class * extends androidx.fragment.app.Fragment

# ================================
# 序列化和Parcelable
# ================================
# 保持Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保持Parcelable
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# ================================
# 反射和JNI
# ================================
# 保持native方法
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保持反射用到的类和方法
-keepclassmembers class * {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# ================================
# 枚举类型
# ================================
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# ================================
# WebView相关
# ================================
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# ================================
# 网络请求相关
# ================================
# Retrofit
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions

# OkHttp
-dontwarn okhttp3.**
-keep class okhttp3.** { *; }
-dontwarn okio.**

# Gson
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# ================================
# 第三方库（根据实际使用调整）
# ================================
# Glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}

# EventBus
-keepattributes *Annotation*
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }

# ================================
# 日志移除
# ================================
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# 移除System.out.println
-assumenosideeffects class java.lang.System {
    public static void out.println(...);
    public static void err.println(...);
}

# ================================
# 高级混淆选项
# ================================
# 类名混淆
-repackageclasses ''
-allowaccessmodification

# 字符串加密（需要ProGuard商业版）
# -adaptclassstrings
# -adaptresourcefilenames
# -adaptresourcefilecontents

# 控制流混淆（需要ProGuard商业版）
# -addconfigurationdebugging

# ================================
# 项目特定配置（需要根据实际项目调整）
# ================================
# 保持自定义的实体类（数据模型）
# -keep class com.yourpackage.model.** { *; }

# 保持自定义的接口
# -keep interface com.yourpackage.api.** { *; }

# 保持自定义的回调接口
# -keep class * implements com.yourpackage.callback.** { *; }

# ================================
# 警告处理
# ================================
# 忽略警告
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn sun.misc.Unsafe

# 保持泛型信息
-keepattributes Signature

# 保持内部类
-keepattributes InnerClasses

# 保持异常信息
-keepattributes Exceptions
