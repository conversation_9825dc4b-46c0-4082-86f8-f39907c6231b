#!/usr/bin/env python3
"""
Android代码保护工具修复脚本
修复build.gradle语法错误和Unicode编码问题
"""

import os
import sys
import re
import shutil
from pathlib import Path

def fix_build_gradle(project_root):
    """修复build.gradle文件中的语法错误"""
    build_gradle = Path(project_root) / "app" / "build.gradle"
    
    if not build_gradle.exists():
        print("[ERROR] 未找到app/build.gradle文件")
        return False
    
    print(f"[INFO] 修复build.gradle文件: {build_gradle}")
    
    # 备份原文件
    backup_file = str(build_gradle) + ".backup"
    if not Path(backup_file).exists():
        shutil.copy2(build_gradle, backup_file)
        print(f"[INFO] 创建备份: {backup_file}")
    
    # 读取文件内容
    with open(build_gradle, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复多余的大括号
    lines = content.split('\n')
    fixed_lines = []
    brace_count = 0
    android_block_started = False
    
    for i, line in enumerate(lines):
        stripped = line.strip()
        
        # 跟踪android块
        if 'android {' in stripped:
            android_block_started = True
        
        # 计算大括号
        brace_count += line.count('{') - line.count('}')
        
        # 检查是否是多余的大括号
        if stripped == '}' and android_block_started:
            # 检查下一行是否是compileOptions或其他android配置
            next_line = lines[i + 1].strip() if i + 1 < len(lines) else ""
            if next_line.startswith('compileOptions') or next_line.startswith('dependencies'):
                print(f"[FIX] 移除第{i+1}行的多余大括号: {stripped}")
                continue
        
        fixed_lines.append(line)
    
    # 写入修复后的内容
    fixed_content = '\n'.join(fixed_lines)
    with open(build_gradle, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print("[SUCCESS] build.gradle文件修复完成")
    return True

def fix_proguard_rules(project_root):
    """更新ProGuard规则以解决R8错误"""
    proguard_file = Path(project_root) / "app" / "proguard-rules.pro"
    
    # 备份原文件
    if proguard_file.exists():
        backup_file = str(proguard_file) + ".backup"
        if not Path(backup_file).exists():
            shutil.copy2(proguard_file, backup_file)
            print(f"[INFO] 创建ProGuard备份: {backup_file}")
    
    # 生成修复后的ProGuard配置
    fixed_proguard_config = """
# Android代码保护 - ProGuard配置 (修复版)
# 解决R8混淆错误和缺失类问题

# 基本混淆设置
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose

# 保持源文件名和行号信息（调试用，发布时可注释）
-keepattributes SourceFile,LineNumberTable

# 保持注解
-keepattributes *Annotation*
-keep class * extends java.lang.annotation.Annotation { *; }

# Android组件保护
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.preference.Preference
-keep public class * extends android.view.View

# Fragment
-keep public class * extends android.app.Fragment
-keep public class * extends android.support.v4.app.Fragment
-keep public class * extends androidx.fragment.app.Fragment

# 序列化和Parcelable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# 反射和JNI
-keepclasseswithmembernames class * {
    native <methods>;
}

-keepclassmembers class * {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# 枚举类型
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 解决javax.lang.model相关错误 - 关键修复
-dontwarn javax.lang.model.**
-dontwarn javax.annotation.processing.**
-dontwarn javax.tools.**
-keep class javax.** { *; }

# Google相关库
-dontwarn com.google.errorprone.annotations.**
-keep class com.google.errorprone.annotations.** { *; }

# AndroidX相关
-keep class androidx.** { *; }
-dontwarn androidx.**
-keep class androidx.lifecycle.** { *; }
-dontwarn androidx.lifecycle.**

# 第三方库通用规则
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-dontwarn okhttp3.**
-keep class okhttp3.** { *; }
-dontwarn okio.**

# 日志移除
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# 移除System.out.println
-assumenosideeffects class java.lang.System {
    public static void out.println(...);
    public static void err.println(...);
}

# 警告处理
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn sun.misc.Unsafe
-dontwarn java.lang.invoke.**

# 保持重要属性
-keepattributes Signature
-keepattributes InnerClasses
-keepattributes Exceptions

# 类名混淆（谨慎使用）
-repackageclasses ''
-allowaccessmodification
"""
    
    # 写入修复后的配置
    proguard_file.parent.mkdir(exist_ok=True)
    with open(proguard_file, 'w', encoding='utf-8') as f:
        f.write(fixed_proguard_config.strip())
    
    print(f"[SUCCESS] ProGuard配置已更新: {proguard_file}")
    return True

def check_and_fix_encoding():
    """检查并设置正确的编码环境"""
    try:
        # 设置环境变量
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # 检查当前编码
        import locale
        print(f"[INFO] 系统编码: {locale.getpreferredencoding()}")
        print(f"[INFO] Python编码: {sys.stdout.encoding}")
        
        return True
    except Exception as e:
        print(f"[WARN] 编码设置警告: {e}")
        return False

def main():
    print("=" * 60)
    print("Android代码保护工具 - 修复脚本")
    print("=" * 60)
    
    # 检查编码环境
    check_and_fix_encoding()
    
    # 获取项目根目录
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = "."
    
    project_path = Path(project_root).resolve()
    print(f"[INFO] 项目路径: {project_path}")
    
    # 检查是否是Android项目
    if not (project_path / "app" / "build.gradle").exists():
        print("[ERROR] 这不是一个有效的Android项目目录")
        print("请确保在Android项目根目录运行此脚本")
        sys.exit(1)
    
    success_count = 0
    
    # 1. 修复build.gradle文件
    print("\n[STEP 1] 修复build.gradle文件...")
    if fix_build_gradle(project_path):
        success_count += 1
    
    # 2. 修复ProGuard规则
    print("\n[STEP 2] 更新ProGuard规则...")
    if fix_proguard_rules(project_path):
        success_count += 1
    
    # 3. 检查android_code_protector.py是否存在
    protector_script = project_path / "android_code_protector.py"
    if not protector_script.exists():
        print(f"\n[WARN] 未找到android_code_protector.py")
        print("请确保将修复后的android_code_protector.py复制到项目根目录")
    else:
        print(f"\n[INFO] 找到android_code_protector.py: {protector_script}")
        success_count += 1
    
    print("\n" + "=" * 60)
    if success_count >= 2:
        print("[SUCCESS] 修复完成！")
        print("\n下一步操作:")
        print("1. 在Android Studio中清理项目: Build -> Clean Project")
        print("2. 重新构建项目: Build -> Rebuild Project")
        print("3. 如果仍有问题，请检查具体的错误信息")
        print("\n测试命令:")
        print("python android_code_protector.py decrypt")
        print("./gradlew assembleRelease")
    else:
        print("[ERROR] 修复过程中出现问题，请检查错误信息")
    print("=" * 60)

if __name__ == '__main__':
    main()
