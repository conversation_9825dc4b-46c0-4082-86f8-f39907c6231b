import os 
import sys
from cryptography.fernet  import Fernet 
import zlib
import argparse 
 
# 生成或加载密钥函数 
def manage_key(key_path='encryption_key.key'): 
    if os.path.exists(key_path): 
        with open(key_path, 'rb') as key_file:
            return key_file.read() 
    else:
        key = Fernet.generate_key() 
        with open(key_path, 'wb') as key_file:
            key_file.write(key) 
        return key
 
# 加密Java文件函数 
def encrypt_java_file(file_path, cipher_suite):
    with open(file_path, 'rb') as file:
        original = file.read() 
    
    # 压缩后加密
    compressed = zlib.compress(original) 
    encrypted = cipher_suite.encrypt(compressed) 
    
    # 生成加密文件名 
    encrypted_path = file_path.replace('.java',  '.jenc')
    with open(encrypted_path, 'wb') as file:
        file.write(encrypted) 
    
    # 删除原始文件
    os.remove(file_path) 
    return encrypted_path 
 
# 解密Java文件函数
def decrypt_java_file(encrypted_path, cipher_suite):
    with open(encrypted_path, 'rb') as file:
        encrypted = file.read() 
    
    # 解密后解压缩 
    compressed = cipher_suite.decrypt(encrypted) 
    original = zlib.decompress(compressed) 
    
    # 还原原始文件名
    original_path = encrypted_path.replace('.jenc',  '.java')
    with open(original_path, 'wb') as file:
        file.write(original) 
    
    # 删除加密文件 
    os.remove(encrypted_path) 
    return original_path
 
# 递归处理目录 
def process_directory(directory, operation, cipher_suite):
    for root, _, files in os.walk(directory): 
        for file in files:
            file_path = os.path.join(root,  file)
            if operation == 'encrypt' and file_path.endswith('.java'): 
                print(f'加密中: {file_path}')
                encrypt_java_file(file_path, cipher_suite)
            elif operation == 'decrypt' and file_path.endswith('.jenc'): 
                print(f'解密中: {file_path}')
                decrypt_java_file(file_path, cipher_suite)
 
# 主函数
def main():
    parser = argparse.ArgumentParser(description='Java文件加密/解密工具')
    parser.add_argument('operation',  choices=['encrypt', 'decrypt'], help='操作类型：encrypt 或 decrypt')
    parser.add_argument('--dir',  default='app/src/main/java', help='要处理的目录路径')
    parser.add_argument('--key',  default='encryption_key.key',  help='密钥文件路径')
    
    args = parser.parse_args() 
    
    # 管理密钥
    key = manage_key(args.key) 
    cipher_suite = Fernet(key)
    
    # 处理目录
    if not os.path.exists(args.dir): 
        print(f"错误：目录不存在 - {args.dir}") 
        return 
    
    process_directory(args.dir,  args.operation,  cipher_suite)
    print(f'{args.operation.capitalize()}  操作完成!')
 
if __name__ == '__main__':
    main()