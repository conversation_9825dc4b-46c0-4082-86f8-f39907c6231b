@echo off
chcp 65001 >nul
title Android代码保护工具

echo.
echo ==========================================
echo    🛡️  Android代码保护工具
echo ==========================================
echo.

:MENU
echo 请选择操作:
echo.
echo [1] 🚀 一键设置代码保护
echo [2] 🔒 加密Java源码
echo [3] 🔓 解密Java源码  
echo [4] 📦 创建源码备份
echo [5] 🔄 恢复源码备份
echo [6] ⚙️  查看配置信息
echo [7] 🧹 清理临时文件
echo [0] ❌ 退出
echo.

set /p choice=请输入选项 (0-7): 

if "%choice%"=="1" goto SETUP
if "%choice%"=="2" goto ENCRYPT
if "%choice%"=="3" goto DECRYPT
if "%choice%"=="4" goto BACKUP
if "%choice%"=="5" goto RESTORE
if "%choice%"=="6" goto CONFIG
if "%choice%"=="7" goto CLEAN
if "%choice%"=="0" goto EXIT

echo 无效选项，请重新选择
pause
goto MENU

:SETUP
echo.
echo 🚀 开始设置代码保护环境...
python setup_protection.py
if %errorlevel% neq 0 (
    echo.
    echo ❌ 设置失败，请检查Python环境和项目结构
) else (
    echo.
    echo ✅ 设置完成！
)
pause
goto MENU

:ENCRYPT
echo.
echo 🔒 开始加密Java源码...
python android_code_protector.py encrypt
if %errorlevel% neq 0 (
    echo.
    echo ❌ 加密失败，请检查配置
) else (
    echo.
    echo ✅ 加密完成！
)
pause
goto MENU

:DECRYPT
echo.
echo 🔓 开始解密Java源码...
python android_code_protector.py decrypt
if %errorlevel% neq 0 (
    echo.
    echo ❌ 解密失败，请检查密钥文件
) else (
    echo.
    echo ✅ 解密完成！
)
pause
goto MENU

:BACKUP
echo.
echo 📦 创建源码备份...
python android_code_protector.py backup
if %errorlevel% neq 0 (
    echo.
    echo ❌ 备份失败
) else (
    echo.
    echo ✅ 备份完成！
)
pause
goto MENU

:RESTORE
echo.
echo 🔄 恢复源码备份...
python android_code_protector.py restore
if %errorlevel% neq 0 (
    echo.
    echo ❌ 恢复失败，请检查备份文件
) else (
    echo.
    echo ✅ 恢复完成！
)
pause
goto MENU

:CONFIG
echo.
echo ⚙️  当前配置信息:
echo.
if exist "code_protection_config.json" (
    type code_protection_config.json
) else (
    echo 配置文件不存在，请先运行设置
)
echo.
if exist "protection_key.key" (
    echo ✅ 密钥文件存在
) else (
    echo ❌ 密钥文件不存在
)
echo.
if exist ".code_backup" (
    echo ✅ 备份目录存在
) else (
    echo ❌ 备份目录不存在
)
pause
goto MENU

:CLEAN
echo.
echo 🧹 清理临时文件...
if exist "*.pyc" del /q *.pyc
if exist "__pycache__" rmdir /s /q __pycache__
if exist "*.tmp" del /q *.tmp
echo ✅ 清理完成！
pause
goto MENU

:EXIT
echo.
echo 👋 感谢使用Android代码保护工具！
echo.
pause
exit

:ERROR
echo.
echo ❌ 发生错误，请检查:
echo 1. Python是否正确安装
echo 2. 是否在Android项目根目录
echo 3. 网络连接是否正常
echo.
pause
goto MENU
