# Android代码保护 - ProGuard配置 (修复版)
# 解决R8混淆错误和缺失类问题

# ================================
# 基本混淆设置
# ================================
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose

# 保持源文件名和行号信息（调试用）
-keepattributes SourceFile,LineNumberTable

# 保持注解
-keepattributes *Annotation*
-keep class * extends java.lang.annotation.Annotation { *; }

# ================================
# Android组件保护
# ================================
# 保持Activity
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.preference.Preference
-keep public class * extends android.view.View

# 保持Fragment
-keep public class * extends android.app.Fragment
-keep public class * extends android.support.v4.app.Fragment
-keep public class * extends androidx.fragment.app.Fragment

# ================================
# 序列化和Parcelable
# ================================
# 保持Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保持Parcelable
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# ================================
# 反射和JNI
# ================================
# 保持native方法
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保持反射用到的类和方法
-keepclassmembers class * {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# ================================
# 枚举类型
# ================================
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# ================================
# 解决javax.lang.model相关错误
# ================================
# 忽略javax.lang.model相关的警告和错误
-dontwarn javax.lang.model.**
-dontwarn javax.annotation.processing.**
-dontwarn javax.tools.**

# 保持javax相关类（如果项目中使用）
-keep class javax.** { *; }

# ================================
# Google相关库
# ================================
# ErrorProne注解
-dontwarn com.google.errorprone.annotations.**
-keep class com.google.errorprone.annotations.** { *; }

# ================================
# 第三方库通用规则
# ================================
# Retrofit
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions

# OkHttp
-dontwarn okhttp3.**
-keep class okhttp3.** { *; }
-dontwarn okio.**

# Gson
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# ================================
# AndroidX相关
# ================================
-keep class androidx.** { *; }
-dontwarn androidx.**

# Lifecycle
-keep class androidx.lifecycle.** { *; }
-dontwarn androidx.lifecycle.**

# ================================
# 日志移除
# ================================
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# 移除System.out.println
-assumenosideeffects class java.lang.System {
    public static void out.println(...);
    public static void err.println(...);
}

# ================================
# 高级混淆选项（谨慎使用）
# ================================
# 类名混淆
-repackageclasses ''
-allowaccessmodification

# ================================
# 警告处理
# ================================
# 忽略常见警告
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn sun.misc.Unsafe
-dontwarn java.lang.invoke.**

# 保持泛型信息
-keepattributes Signature

# 保持内部类
-keepattributes InnerClasses

# 保持异常信息
-keepattributes Exceptions

# ================================
# 项目特定配置（根据需要调整）
# ================================
# 如果你的项目使用了特定的库，请在这里添加相应的keep规则

# 保持自定义的实体类（数据模型）
# -keep class com.yourpackage.model.** { *; }

# 保持自定义的接口
# -keep interface com.yourpackage.api.** { *; }

# 保持自定义的回调接口
# -keep class * implements com.yourpackage.callback.** { *; }
